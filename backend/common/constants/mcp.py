"""
MCP (Model Context Protocol) server configuration.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from config import get_config
from config.core.base_config import DotDict


@dataclass
class MCPServerConfig:
    """Configuration for a single MCP server."""
    name: str
    transport: str
    enabled_tools: List[str]
    add_to_agents: List[str]
    env: Optional[Dict[str, str]] = None
    url: Optional[str] = None
    command: Optional[str] = None
    args: Optional[List[str]] = None
    headers: Optional[Dict[str, str]] = None


def get_default_headers(config: Optional[Any] = None) -> Dict[str, str]:
    """
    Get default headers for MCP requests.

    Args:
        config: Configuration object. If None, will call get_config()

    Note: In production, this should be loaded from encrypted configuration.
    """
    if config is None:
        config = get_config()

    # Use the diagnose server token (which maps to cloudbot)
    token = config.mcp_servers.diagnose.token
    return {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }


def get_mcp_settings(config: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get MCP server settings configuration.

    Args:
        config: Configuration object. If None, will call get_config()

    Returns:
        Dict containing MCP server configurations
    """
    if config is None:
        config = get_config()

    servers = {}

    # Check if mcp_servers configuration exists
    if not hasattr(config, 'mcp_servers'):
        return {"servers": servers}

    # Process all MCP servers directly (flattened structure)
    mcp_servers_dict = config.mcp_servers if isinstance(config.mcp_servers, dict) else config.mcp_servers.__dict__

    for server_name, server_config in mcp_servers_dict.items():
        # Convert to DotDict if it's a regular dict
        if isinstance(server_config, dict):
            server_config = DotDict(server_config)

        # Determine the final server name for output
        output_server_name = _get_output_server_name(server_name)

        # Build server configuration
        mcp_config = _build_server_config(
            output_server_name,
            server_config
        )

        if mcp_config:
            servers[output_server_name] = mcp_config

    return {"servers": servers}


def _get_output_server_name(server_name: str) -> str:
    """
    Determine the output server name based on server name.

    Args:
        server_name: Name of the server (e.g., 'vm_coredump', 'diagnose', 'antv')

    Returns:
        str: The final server name to use in output
    """
    # Special naming rules for backward compatibility
    if server_name == "diagnose":
        return "cloudbot"  # diagnose server maps to cloudbot
    elif server_name == "vm_coredump":
        return "vm_coredump"  # keep as is
    elif server_name == "antv":
        return "antv"  # keep as is
    else:
        # Default: use server_name as is
        return server_name


def _build_server_config(server_name: str, server_config: Any) -> Optional[Dict[str, Any]]:
    """
    Build MCP server configuration from server config.

    Args:
        server_name: Name of the server
        server_config: Server configuration

    Returns:
        Optional[Dict[str, Any]]: Server configuration dict or None if invalid
    """
    try:
        # Get transport protocol
        transport = getattr(server_config, 'protocol', 'streamable_http')

        # Build base configuration
        config_kwargs = {
            'name': server_name,
            'transport': transport,
            'enabled_tools': getattr(server_config, 'enabled_tools', []),
            'add_to_agents': ["researcher"]  # Default to researcher
        }

        # Handle different transport types
        if transport == 'stdio':
            # For stdio transport, use command and args
            config_kwargs['command'] = "npx"  # Default command for stdio
            config_kwargs['args'] = getattr(server_config, 'args', [])
        else:
            # For HTTP-based transports, build URL and headers
            url = _build_server_url(server_config)
            if url:
                config_kwargs['url'] = url

            # Build headers if needed
            headers = _build_server_headers(server_config)
            if headers:
                config_kwargs['headers'] = headers

        return MCPServerConfig(**config_kwargs).__dict__

    except Exception as e:
        # Log error and skip this server
        print(f"Warning: Failed to build config for server {server_name}: {e}")
        return None


def _build_server_url(server_config: Any) -> Optional[str]:
    """
    Build server URL from server configuration.

    Args:
        server_config: Server configuration

    Returns:
        Optional[str]: Complete server URL or None
    """
    base_url = getattr(server_config, 'base_url', '')
    if not base_url:
        return None

    path = getattr(server_config, 'path', '')
    url = f"{base_url.rstrip('/')}{path}"

    # Add token to URL if using token auth
    auth_type = getattr(server_config, 'auth', '')
    token = getattr(server_config, 'token', '')

    if auth_type == 'token' and token:
        url = f"{url}?token={token}"

    return url


def _build_server_headers(server_config: Any) -> Optional[Dict[str, str]]:
    """
    Build server headers from server configuration.

    Args:
        server_config: Server-specific configuration

    Returns:
        Optional[Dict[str, str]]: Headers dict or None
    """
    auth_type = getattr(server_config, 'auth', '')
    token = getattr(server_config, 'token', '')

    if auth_type == 'bearer' and token:
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }

    return None

__all__ = [
    'MCPServerConfig',
    'get_mcp_settings',


]
