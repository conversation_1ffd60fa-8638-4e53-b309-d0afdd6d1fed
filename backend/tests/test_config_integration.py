"""
Integration tests for the complete configuration system.

Tests the full configuration loading process including environment detection,
file selection, and configuration merging.
"""

import os
import unittest
from unittest import TestCase

from backend.config import get_config


class TestConfigIntegration(TestCase):
    """Test complete configuration system integration."""

    def setUp(self):
        """Set up test environment."""
        # Store original environment variable
        self.original_app_env = os.environ.get('app_env')
        # Clear config cache to ensure fresh loading
        import backend.config
        backend.config._config_cache = None
    
    def tearDown(self):
        """Clean up test environment."""
        # Restore original environment variable
        if self.original_app_env is not None:
            os.environ['app_env'] = self.original_app_env
        elif 'app_env' in os.environ:
            del os.environ['app_env']
        
        # Clear config cache
        import backend.config
        backend.config._config_cache = None

    def test_config_loads_daily_environment(self):
        """Test that config system loads daily configuration correctly."""
        os.environ['app_env'] = 'daily'
        
        config = get_config()
        
        # Check environment settings
        self.assertEqual(config.app_env, 'daily')
        self.assertEqual(config.environment, 'daily')
        self.assertFalse(config.is_production)
        
        # Check that daily-specific config is loaded
        # Daily uses test login host
        self.assertEqual(config.auth.buc_sso.host, 'https://login-test.alibaba-inc.com')
        
        # Check app settings
        self.assertEqual(config.app.name, 'ecs-deep-diagnose')
        self.assertEqual(config.app.port, 8000)

    def test_config_loads_prod_environment(self):
        """Test that config system loads production configuration correctly."""
        os.environ['app_env'] = 'prod'
        
        config = get_config()
        
        # Check environment settings
        self.assertEqual(config.app_env, 'prod')
        self.assertEqual(config.environment, 'prod')
        self.assertTrue(config.is_production)
        
        # Check that prod-specific config is loaded
        # Prod uses production login host
        self.assertEqual(config.auth.buc_sso.host, 'https://login.alibaba-inc.com')
        
        # Check app settings
        self.assertEqual(config.app.name, 'ecs-deep-diagnose')
        self.assertEqual(config.app.port, 8000)

    def test_config_loads_pre_environment_with_prod_config(self):
        """Test that pre environment uses production configuration."""
        os.environ['app_env'] = 'pre'
        
        config = get_config()
        
        # Check environment settings
        self.assertEqual(config.app_env, 'pre')
        self.assertEqual(config.environment, 'pre')
        self.assertTrue(config.is_production)  # pre is considered production
        
        # Check that prod config file is used (same as prod)
        self.assertEqual(config.auth.buc_sso.host, 'https://login.alibaba-inc.com')

    def test_config_has_mcp_settings(self):
        """Test that MCP settings are properly loaded."""
        os.environ['app_env'] = 'daily'
        
        config = get_config()
        
        # Check that MCP settings exist
        self.assertIn('mcp_settings', config)
        self.assertIsInstance(config.mcp_settings, dict)

    def test_config_has_required_sections(self):
        """Test that all required configuration sections are present."""
        os.environ['app_env'] = 'daily'
        
        config = get_config()
        
        # Check required top-level sections
        required_sections = ['app', 'auth', 'mcp_servers', 'security', 'llm']
        for section in required_sections:
            self.assertIn(section, config, f"Missing required section: {section}")
        
        # Check app section
        self.assertIn('name', config.app)
        self.assertIn('port', config.app)
        
        # Check auth section
        self.assertIn('buc_sso', config.auth)
        self.assertIn('jwt_users', config.auth)

    def test_config_caching_works(self):
        """Test that configuration caching works properly."""
        os.environ['app_env'] = 'daily'

        # First call should load config
        config1 = get_config()

        # Second call should return cached config (values should be identical)
        config2 = get_config()

        # Values should be identical (testing that cache works)
        self.assertEqual(config1.app_env, config2.app_env)
        self.assertEqual(config1.app.name, config2.app.name)
        self.assertEqual(config1.auth.buc_sso.host, config2.auth.buc_sso.host)

        # Test that the underlying cache is working by checking internal state
        import backend.config
        self.assertIsNotNone(backend.config._config_cache)


if __name__ == '__main__':
    unittest.main()
