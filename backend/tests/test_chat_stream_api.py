
import json
import pytest
import httpx
import asyncio
import uuid

# 集成测试：直接测试运行在localhost:8000上的服务
# 运行测试前，请确保服务已在localhost:8000端口启动
# 可以通过以下命令启动服务：
# cd backend && python server.py


@pytest.mark.asyncio
async def test_integration_valid_user_password():
    timeout = httpx.Timeout(600.0, connect=5.0)

    """测试用户名密码验证"""
    async with httpx.AsyncClient(base_url="http://localhost:8000", timeout=timeout) as client:
        # 构建请求数据
        request_data = {
            "access_key": "admin",
            "secret_key": "admin",
            "token_lifetime_minutes": 60
        }

        # 发送POST请求到验证API
        response = await client.post(
            "/api/token",
            json=request_data
        )
        assert response.status_code == 200
        content = json.loads(response.content)
        assert content is not None

        next_request_data = {
            "messages": [
                {
                    "role": "user",
                    "content": "分析实例 i-gw86e9lj0cmatoxvtupw、i-gw8b1l5gwdwmw5wppzeh i-bp19trejji1vfbn5sggr、i-bp1abyl7xsjkdegxvsv0、i-bp1hlixo95umodxtqu2z、i-bp11mm66jlxxe6fxyf5w、i-bp1g9g63x9pgkhfzjyym i-bp1hlixo95umodxtqu2z、i-bp18nq0n1r3n8t43x6kf、i-bp1g9g63x9pgkhfzjyym、i-bp1abyl7xsjkdegxvsuy的4月份运维事件，判断其原因是否有共性。"
                }
            ],
            "thread_id": str(uuid.uuid4()),  # 使用随机生成的thread_id
            "auto_accepted_plan": False,
            "enable_background_investigation": False,
            "max_plan_iterations": 1,
            "max_step_num": 3,
            "max_search_results": 3
        }

        # 发送POST请求到聊天流式API
        next_response = await client.post(
            "/api/chat/stream",
            json=next_request_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer "+content.get("access_token")
            }
        )

        # 验证响应状态码
        assert next_response.status_code == 200

        # 验证响应内容类型
        #assert next_response.headers["content-type"] == "text/event-stream"

        # 读取并验证流式响应内容 - 获取完整响应
        response_chunks = []
        async for chunk in next_response.aiter_text():
            response_chunks.append(chunk)

        # 合并所有响应块
        full_response = "".join(response_chunks)

        # 验证响应包含预期的数据格式
        assert "data:" in full_response

        # 解析所有事件数据
        events = []
        for line in full_response.split("\n"):
            if line.startswith("data:"):
                try:
                    event_data = json.loads(line[5:].strip())
                    events.append(event_data)
                except json.JSONDecodeError:
                    continue
        # 验证至少收到了一个事件
        assert len(events) > 0

        # 验证事件数据结构
        for event in events:
            assert "thread_id" in event
            assert event["thread_id"] == next_request_data["thread_id"]

        # 验证响应中包含了某些关键词（这里我们期望响应中包含一些关于ECS实例的信息）
        assert any("实例" in event.get("content", "") for event in events if "content" in event)

asyncio.run(test_integration_valid_user_password())
