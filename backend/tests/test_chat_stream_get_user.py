import pytest
import httpx
from datetime import datetime, timedelta
from jose import jwt



# 假设服务运行在本地端口 8000
BASE_URL = "http://localhost:8000"

# 从 config 获取密钥和算法
from config import get_config

SECRET_KEY = get_config().app.secret
ALGORITHM = "HS256"


def generate_token(subject: str, expires_delta: timedelta = None):
    """生成一个有效的 JWT Token"""
    expire = datetime.utcnow() + (expires_delta if expires_delta else timedelta(minutes=15))
    to_encode = {"sub": subject, "exp": expire}
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)


@pytest.mark.asyncio
async def test_stream_endpoint_requires_jwt():
    """TC001: 未携带 Token 请求 /api/chat/stream 应拒绝访问"""
    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        response = await client.post("/api/chat/stream", json={"messages": []})
        assert response.status_code == 307


@pytest.mark.asyncio
async def test_stream_with_invalid_token():
    """TC002: 使用非法 Token 请求 /api/chat/stream 应拒绝访问"""
    headers = {"Authorization": "Bearer invalid.token.here"}
    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        response = await client.post("/api/chat/stream", json={"messages": []}, headers=headers)
        assert response.status_code == 401


@pytest.mark.asyncio
async def test_stream_with_valid_token():
    """TC003: 使用有效 Token 请求 /api/chat/stream 应成功"""
    token = generate_token("user123")
    headers = {"Authorization": f"Bearer {token}"}
    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        response = await client.post(
            "/api/chat/stream",
            json={"messages": [{"role": "user", "content": "test"}]},
            headers=headers
        )
        assert response.status_code == 401
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"


@pytest.mark.asyncio
async def test_refresh_token_endpoint():
    """TC004: 测试刷新 Token 接口"""
    old_token = generate_token("user123", expires_delta=timedelta(seconds=1))
    headers = {"Authorization": f"Bearer {old_token}"}

    # 等待 Token 过期
    import time
    time.sleep(2)

    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        response = await client.post("/token/refresh", headers=headers)
        assert response.status_code == 200
        new_token = response.json()["access_token"]
        decoded = jwt.decode(new_token, SECRET_KEY, algorithms=[ALGORITHM])
        assert decoded["sub"] == "user123"


@pytest.mark.asyncio
async def test_whitelisted_url_skips_auth():
    """TC005: 白名单 URL 如 /token 不需要 Token 即可访问"""
    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        response = await client.post("/token", json={"access_key": "test", "secret_key": "test"})
        assert response.status_code in [200, 401, 307]  # 根据实际接口设计调整


@pytest.mark.asyncio
async def test_buc_session_user_access():
    """TC006: 测试 BUC Session 用户访问受保护的 API"""
    # 模拟 BUC 登录：设置 session cookie
    async with httpx.AsyncClient(base_url=BASE_URL, follow_redirects=True) as client:
        # 先模拟登录（根据实际 BUC 登录流程）
        login_response = await client.get("/sendBucSSOToken.do?SSO_TOKEN=dummy&BACK_URL=/api/current-user")
        assert login_response.status_code == 200  # 重定向表示登录成功

        # 再请求 protected endpoint
        chat_response = await client.post("/api/chat/stream", json={"messages": []})
        assert chat_response.status_code == 200  # 应该允许访问
