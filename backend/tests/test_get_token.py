import pytest
from unittest.mock import patch
from fastapi import status
from fastapi.responses import JSONResponse

# 导入被测模块
from web.api.routes.get_token import check_user_auth, token

@pytest.fixture
def mock_get_config():
    from config import get_config
    local_users = get_config().auth.jwt_users
    return local_users


# 测试 check_user_auth 函数
def test_check_user_auth_valid_credentials(mock_get_config):
    assert check_user_auth("admin", "admin") is True


def test_check_user_auth_invalid_username(mock_get_config):
    assert check_user_auth("wronguser", "admin") is False


def test_check_user_auth_invalid_password(mock_get_config):
    assert check_user_auth("admin", "wrongpass") is False


# 测试 token 函数
@patch('web.api.routes.get_token.check_user_auth', return_value=True)
@patch('web.auth.auth_utils.create_jwt_access_token', return_value="mocked-token")
def test_token_valid_credentials(mock_create_token, mock_check_auth):
    from pydantic import BaseModel

    class TokenRequestModel(BaseModel):
        access_key: str = "admin"
        secret_key: str = "admin"
        token_lifetime_minutes: int = 1

    response = token(TokenRequestModel())
    assert isinstance(response, JSONResponse)
    assert response.status_code == status.HTTP_200_OK


@patch('web.api.routes.get_token.check_user_auth', return_value=False)
def test_token_invalid_credentials(mock_check_auth):
    from pydantic import BaseModel
    class TokenRequestModel(BaseModel):
        access_key: str = "wronguser"
        secret_key: str = "wrongpass"
        token_lifetime_minutes: int = 1

    response = token(TokenRequestModel())
    assert isinstance(response, JSONResponse)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


@patch('web.api.routes.get_token.check_user_auth', return_value=True)
@patch('web.auth.auth_utils.create_jwt_access_token', return_value="mocked-token")
def test_token_with_custom_expiry(mock_create_token, mock_check_auth):
    from pydantic import BaseModel
    class TokenRequestModel(BaseModel):
        access_key: str = "admin"
        secret_key: str = "admin"
        token_lifetime_minutes: int = 60

    response = token(TokenRequestModel())
    assert isinstance(response, JSONResponse)
    assert response.status_code == status.HTTP_200_OK
