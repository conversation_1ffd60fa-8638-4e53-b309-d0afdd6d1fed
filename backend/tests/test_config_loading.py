"""
Unit tests for configuration loading functionality.

Tests the environment-based configuration file loading without using mocks.
"""

import os
import tempfile
import unittest
from unittest import TestCase

from backend.config.core.environment import Environment, get_environment, get_config_file_name
from backend.config.loaders.yaml_loader import YamlConfigLoader


class TestEnvironmentConfigLoading(TestCase):
    """Test environment-based configuration loading."""

    def setUp(self):
        """Set up test environment."""
        # Store original environment variable
        self.original_app_env = os.environ.get('app_env')
    
    def tearDown(self):
        """Clean up test environment."""
        # Restore original environment variable
        if self.original_app_env is not None:
            os.environ['app_env'] = self.original_app_env
        elif 'app_env' in os.environ:
            del os.environ['app_env']

    def test_get_environment_development_default(self):
        """Test that development is the default environment."""
        # Remove app_env if it exists
        if 'app_env' in os.environ:
            del os.environ['app_env']
        
        env = get_environment()
        self.assertEqual(env, Environment.DAILY)

    def test_get_environment_daily(self):
        """Test getting daily environment."""
        os.environ['app_env'] = 'daily'
        env = get_environment()
        self.assertEqual(env, Environment.DAILY)

    def test_get_environment_prod(self):
        """Test getting production environment."""
        os.environ['app_env'] = 'prod'
        env = get_environment()
        self.assertEqual(env, Environment.PROD)

    def test_get_config_file_name_daily(self):
        """Test getting config file name for daily environment."""
        os.environ['app_env'] = 'daily'
        config_file = get_config_file_name()
        self.assertEqual(config_file, 'config_daily.yaml')

    def test_get_config_file_name_prod(self):
        """Test getting config file name for production environment."""
        os.environ['app_env'] = 'prod'
        config_file = get_config_file_name()
        self.assertEqual(config_file, 'config_prod.yaml')

    def test_get_config_file_name_pre(self):
        """Test getting config file name for pre environment (should use prod config)."""
        os.environ['app_env'] = 'pre'
        config_file = get_config_file_name()
        self.assertEqual(config_file, 'config_prod.yaml')

    def test_get_config_file_name_development(self):
        """Test getting config file name for development environment."""
        os.environ['app_env'] = 'development'
        config_file = get_config_file_name()
        self.assertEqual(config_file, 'config_daily.yaml')


class TestYamlConfigLoader(TestCase):
    """Test YAML configuration loader."""

    def setUp(self):
        """Set up test environment."""
        self.original_app_env = os.environ.get('app_env')
        # Create temporary config files for testing
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test config files
        self.daily_config_content = """
app:
  name: test-app
  port: 8000
  environment: daily

test_setting: daily_value
"""
        
        self.prod_config_content = """
app:
  name: test-app
  port: 8000
  environment: prod

test_setting: prod_value
"""
        
        with open(os.path.join(self.temp_dir, 'config_daily.yaml'), 'w') as f:
            f.write(self.daily_config_content)
        
        with open(os.path.join(self.temp_dir, 'config_prod.yaml'), 'w') as f:
            f.write(self.prod_config_content)

    def tearDown(self):
        """Clean up test environment."""
        # Restore original environment variable
        if self.original_app_env is not None:
            os.environ['app_env'] = self.original_app_env
        elif 'app_env' in os.environ:
            del os.environ['app_env']
        
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_yaml_loader_loads_daily_config(self):
        """Test that YAML loader loads daily config for daily environment."""
        os.environ['app_env'] = 'daily'
        
        loader = YamlConfigLoader(config_dir=self.temp_dir)
        config = loader.load()
        
        self.assertEqual(config['test_setting'], 'daily_value')
        self.assertEqual(config['app']['environment'], 'daily')
        self.assertEqual(config['app_env'], 'daily')

    def test_yaml_loader_loads_prod_config(self):
        """Test that YAML loader loads prod config for production environment."""
        os.environ['app_env'] = 'prod'
        
        loader = YamlConfigLoader(config_dir=self.temp_dir)
        config = loader.load()
        
        self.assertEqual(config['test_setting'], 'prod_value')
        self.assertEqual(config['app']['environment'], 'prod')
        self.assertEqual(config['app_env'], 'prod')

    def test_yaml_loader_loads_prod_config_for_pre_env(self):
        """Test that YAML loader loads prod config for pre environment."""
        os.environ['app_env'] = 'pre'
        
        loader = YamlConfigLoader(config_dir=self.temp_dir)
        config = loader.load()
        
        self.assertEqual(config['test_setting'], 'prod_value')
        self.assertEqual(config['app']['environment'], 'prod')
        self.assertEqual(config['app_env'], 'pre')  # app_env should reflect actual environment

    def test_yaml_loader_handles_missing_config_file(self):
        """Test that YAML loader handles missing config file gracefully."""
        os.environ['app_env'] = 'development'  # Use valid environment

        # Create loader with empty directory
        empty_dir = tempfile.mkdtemp()
        try:
            loader = YamlConfigLoader(config_dir=empty_dir)
            config = loader.load()

            # Should return config with app_env set, but no other settings
            self.assertEqual(config['app_env'], 'development')
            self.assertNotIn('test_setting', config)
            # Should have app_queue set as well
            self.assertEqual(config['app_queue'], 'ecs-deep-diagnose_development')
        finally:
            import shutil
            shutil.rmtree(empty_dir)


if __name__ == '__main__':
    unittest.main()
