

from typing import Any, Dict

from langchain_openai import Chat<PERSON>penA<PERSON>

from config import get_config
from common.constants.agents import LLMType
from config.core.base_config import DotDict
from llms.chat_qwen import Chat<PERSON><PERSON>

# Cache for LLM instances
_llm_cache: dict[LLMType, ChatOpenAI] = {}


def _create_llm_use_conf(llm_type: LLMType, conf: DotDict) -> ChatOpenAI:
    llm_type_map = {
        "reasoning": conf.llm.profiles.reasoning,
        "basic": conf.llm.profiles.basic,
        "vision": conf.llm.profiles.vision,
    }
    llm_conf = llm_type_map.get(llm_type)
    if not llm_conf:
        raise ValueError(f"Unknown LLM type: {llm_type}")
    if not isinstance(llm_conf, dict):
        raise ValueError(f"Invalid LLM Conf: {llm_type}")
    return ChatQwen(**llm_conf)


def get_llm_by_type(
    llm_type: LLMType,
) -> ChatOpenAI:
    """
    Get LLM instance by type. Returns cached instance if available.
    """
    if llm_type in _llm_cache:
        return _llm_cache[llm_type]
    conf = get_config()
    llm = _create_llm_use_conf(llm_type, conf)
    _llm_cache[llm_type] = llm
    return llm


# In the future, we will use reasoning_llm and vl_llm for different purposes
reasoning_llm = get_llm_by_type("reasoning")
# vl_llm = get_llm_by_type("vision")


if __name__ == "__main__":
    # Initialize LLMs for different purposes - now these will be cached
    basic_llm = get_llm_by_type("basic")
    print(basic_llm.invoke("Hello"))
