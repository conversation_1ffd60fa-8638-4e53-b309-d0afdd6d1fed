import logging

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from langfuse.callback import <PERSON><PERSON><PERSON><PERSON><PERSON>
from starlette.middleware.sessions import SessionMiddleware

from graph.builder import build_graph_with_memory
from logging_config import configure_logging
from web.middleware import AuthMiddleware
from config import get_config
# Configure logging
configure_logging()
langfuse_handler = CallbackHandler(public_key=get_config().observability.langfuse.public_key, secret_key=get_config().observability.langfuse.secret_key, host="https://chatecs-trace.alibaba-inc.com")

logger = logging.getLogger(__name__)

app = FastAPI(
    title="ECS Deep Diagnose API",
    description="API for Deer",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

app.add_middleware(AuthMiddleware)
app.add_middleware(SessionMiddleware, secret_key=get_config().app.secret)  # 请确保这是一个安全的密钥，最好从配置中读取

graph = build_graph_with_memory()



# 导入路由模块
from web.api.routes import auth, chat, token

# 注册路由
app.include_router(auth.router)
app.include_router(chat.router)
app.include_router(token.router)
