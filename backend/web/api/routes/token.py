"""token相关的API路由处理模块

本模块包含与验证用户token的API路由处理函数。
"""

from datetime import timedelta

from fastapi import APIRouter
from fastapi import status
from fastapi.responses import JSONResponse

from web.api.model.request_model import TokenRequestModel
from web.auth.auth_utils import create_jwt_access_token
from web.auth.constants import ACCESS_TOKEN_EXPIRE_MINUTES

router = APIRouter()


def check_user_auth(access_key: str, secret_key: str):
    from config import get_config
    local_users = get_config().auth.jwt_users
    for user in local_users:
        if user["user_name"] == access_key and user["password"] == secret_key:
            return True
    return False


@router.post("/api/token")
def token(token_request: TokenRequestModel):
    access_key = token_request.access_key
    secret_key = token_request.secret_key
    token_lifetime_minutes = token_request.token_lifetime_minutes
    if not check_user_auth(access_key, secret_key):
        return JSONResponse(
            content={"msg": "Bad access_key or secret_key"},
            status_code=status.HTTP_401_UNAUTHORIZED,
        )
    access_token_expires = timedelta(minutes=token_lifetime_minutes or ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_jwt_access_token(
        data={"sub": access_key}, expires_delta=access_token_expires
    )
    return JSONResponse(content={"access_token": access_token})


