// app/lib/userInfo.ts
export interface UserInfo {
  id: string;
  nickNameCn: string | null;
  realname: string;
  emailAddr: string;
}

export const defaultUserInfo: UserInfo = {
  id: "unknown",
  nickNameCn: "未知用户",
  realname: "未知用户",
  emailAddr: "",
};

// 定义 API 响应的整体结构
interface ApiResponse {
  status: number;
  request_uuid: string;
  data: UserInfo;
  error_message: string;
}

function redirectToLogin(): void {
  // 1. 获取当前页面的完整 URL
  const currentUrl = window.location.href;

  // 2. 对 URL 进行编码，以安全地作为查询参数传递
  const encodedBackUrl = encodeURIComponent(currentUrl);

  // 声明一个变量来存储最终的登录 URL
  let loginUrl: string;

  // 3. 判断当前 URL 是否包含生产环境域名
  if (currentUrl.includes('ecs-deep-diagnose.aliyun-inc.com')) {
    // 如果是生产环境，使用正式的登录地址
    loginUrl = `https://login.alibaba-inc.com/ssoLogin.htm?APP_NAME=ecs-deep-diagnose&BACK_URL=${encodedBackUrl}`;
  } else {
    // 否则，使用测试环境的登录地址
    loginUrl = `https://login-test.alibaba-inc.com/ssoLogin.htm?APP_NAME=ecs-deep-diagnose&BACK_URL=${encodedBackUrl}`;
  }

  // 4. 执行跳转
    if (currentUrl.includes('ecs-deep-diagnose.aliyun-inc.com')) {
     window.location.href = loginUrl;
  }
}

export async function fetchUserInfo(): Promise<UserInfo> {
  try {
    const response = await fetch("/public/buc/getUser", {
      method: "GET",
      headers: {
        'Accept': 'application/json',
      },
      // 注意: 标准的 fetch API 默认会自动跟随3xx重定向。
      // 为了能够捕获到 307 状态码并手动处理，后端服务器需要正确配置CORS策略，
      // 允许前端脚本访问 'Location' 响应头。
    });


    if (response.status !== 200) {
      redirectToLogin();
      return defaultUserInfo;

    }

      const apiResponse: ApiResponse = await response.json();

      if (apiResponse.data) {
        return apiResponse.data;
      } else {
        console.error("Fetched data is missing the 'data' field or is malformed:", apiResponse);
        return defaultUserInfo;
      }
  } catch (error) {
      redirectToLogin();
    return defaultUserInfo;
  }
}
