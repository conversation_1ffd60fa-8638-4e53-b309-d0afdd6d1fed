<svg width="1600" height="1400" viewBox="0 0 1600 1400" xmlns="http://www.w3.org/2000/svg" font-family="'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif">
  
  <defs>
    <!-- Enhanced Gradients -->
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fafbfc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="header-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="value-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fef3c7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="app-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#eff6ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dbeafe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="engine-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f0fdf4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dcfce7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="platform-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fefce8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:1" />
    </linearGradient>
    
    <!-- Enhanced Arrow Marker -->
    <marker id="arrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,8 L12,4 z" fill="#4f46e5" opacity="0.8"/>
    </marker>
    
    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- Styles -->
    <style>
      .main-title { 
        font-size: 32px; 
        font-weight: 700; 
        fill: white; 
        text-anchor: middle; 
        letter-spacing: 0.5px;
      }
      
      .layer-title { 
        font-size: 24px; 
        font-weight: 600; 
        fill: #1e3a8a; 
        text-anchor: middle; 
        letter-spacing: 0.3px;
      }
      
      .value-title { 
        font-size: 18px; 
        font-weight: 700; 
        fill: #92400e; 
        text-anchor: middle; 
      }
      
      .value-desc { 
        font-size: 13px; 
        fill: #78350f; 
        font-weight: 400;
        line-height: 1.4;
      }
      
      .box-title { 
        font-size: 16px; 
        font-weight: 600; 
        fill: #1e40af; 
        text-anchor: middle; 
      }
      
      .feature-item { 
        font-size: 12px; 
        fill: #475569; 
        font-weight: 400;
      }
      
      .feature-highlight { 
        font-size: 12px; 
        fill: #1e40af; 
        font-weight: 500;
      }
      
      .connection-line { 
        stroke: #4f46e5; 
        stroke-width: 2; 
        fill: none; 
        marker-end: url(#arrow);
        opacity: 0.7;
      }
      
      .layer-separator {
        stroke: #e2e8f0;
        stroke-width: 1;
        opacity: 0.5;
      }
    </style>
  </defs>

  <!-- Background -->
  <rect width="1600" height="1400" fill="url(#bg-gradient)" />

  <!-- Header Section -->
  <rect x="0" y="0" width="1600" height="80" fill="url(#header-gradient)" filter="url(#drop-shadow)" />
  <text x="800" y="50" class="main-title">CloudBot 智能运维系统 - 核心价值架构</text>
  
  <!-- Core Values Section -->
  <g transform="translate(50, 100)">
    <text x="750" y="30" class="layer-title">🎯 三大核心价值主张</text>

    <!-- Value Proposition 1 -->
    <g transform="translate(0, 60)">
      <rect x="0" y="0" width="480" height="110" rx="12" fill="url(#value-gradient)" filter="url(#drop-shadow)" />
      <text x="240" y="30" class="value-title">🔍 智能感知与精准诊断</text>
      <text x="20" y="55" class="value-desc">
        <tspan x="20" dy="0">基于AIOps算法实现全链路智能监控，变被动运维为</tspan>
        <tspan x="20" dy="16">主动预警，秒级定界定位，让系统健康状态一目了然</tspan>
      </text>
      <!-- Value indicator -->
      <circle cx="240" cy="120" r="8" fill="#f59e0b" opacity="0.8"/>
      <text x="240" y="125" style="font-size: 10px; fill: white; text-anchor: middle; font-weight: bold;">1</text>
    </g>

    <!-- Value Proposition 2 -->
    <g transform="translate(510, 60)">
      <rect x="0" y="0" width="480" height="110" rx="12" fill="url(#value-gradient)" filter="url(#drop-shadow)" />
      <text x="240" y="30" class="value-title">🛡️ 智能变更与自愈防护</text>
      <text x="20" y="55" class="value-desc">
        <tspan x="20" dy="0">融合知识图谱与智能编排，提供零风险灰度发布、</tspan>
        <tspan x="20" dy="16">毫秒级熔断响应，构建业务连续性的最后一道防线</tspan>
      </text>
      <!-- Value indicator -->
      <circle cx="240" cy="120" r="8" fill="#f59e0b" opacity="0.8"/>
      <text x="240" y="125" style="font-size: 10px; fill: white; text-anchor: middle; font-weight: bold;">2</text>
    </g>

    <!-- Value Proposition 3 -->
    <g transform="translate(1020, 60)">
      <rect x="0" y="0" width="480" height="110" rx="12" fill="url(#value-gradient)" filter="url(#drop-shadow)" />
      <text x="240" y="30" class="value-title">📊 数据驱动与智能进化</text>
      <text x="20" y="55" class="value-desc">
        <tspan x="20" dy="0">构建运维数据闭环，通过智能分析沉淀最佳实践，</tspan>
        <tspan x="20" dy="16">让每次故障都成为系统进化的催化剂</tspan>
      </text>
      <!-- Value indicator -->
      <circle cx="240" cy="120" r="8" fill="#f59e0b" opacity="0.8"/>
      <text x="240" y="125" style="font-size: 10px; fill: white; text-anchor: middle; font-weight: bold;">3</text>
    </g>
  </g>

  <!-- Layer 1: Application Layer -->
  <g transform="translate(50, 290)">
    <text x="750" y="30" class="layer-title">📱 产品应用层 - 价值实现载体</text>
    <rect x="0" y="50" width="1500" height="320" rx="15" fill="white" stroke="#e2e8f0" stroke-width="1" filter="url(#drop-shadow)" />

    <!-- Value Connection Lines -->
    <g opacity="0.3">
      <path d="M240,0 L200,50" stroke="#f59e0b" stroke-width="2" stroke-dasharray="5,5"/>
      <path d="M750,0 L560,50 L920,50" stroke="#f59e0b" stroke-width="2" stroke-dasharray="5,5"/>
      <path d="M1260,0 L1280,50" stroke="#f59e0b" stroke-width="2" stroke-dasharray="5,5"/>
    </g>
    
    <!-- App Module 1: Collection &amp; Diagnosis - PRIMARY -->
    <g transform="translate(30, 80)">
      <rect x="0" y="0" width="340" height="260" rx="10" fill="url(#app-gradient)" stroke="#1e40af" stroke-width="3" />
      <circle cx="20" cy="20" r="12" fill="#f59e0b"/>
      <text x="20" y="25" style="font-size: 10px; fill: white; text-anchor: middle; font-weight: bold;">1</text>
      <text x="170" y="35" class="box-title">🔍 智能感知诊断中心</text>
      <text x="20" y="65" class="feature-highlight">🎯 价值1核心：主动感知 + 精准诊断</text>
      <text x="20" y="90" class="feature-item">⚡ 秒级定界：全链路实时追踪</text>
      <text x="20" y="110" class="feature-item">🧠 智能分析：影响面自动评估</text>
      <text x="20" y="130" class="feature-item">🩺 健康诊断：实例状态监控</text>
      <text x="20" y="150" class="feature-item">🤖 AI助手：根因推荐引擎</text>
      <text x="20" y="175" class="feature-highlight">💡 关键突破：</text>
      <text x="20" y="195" class="feature-item">被动→主动 | 人工→智能 | 模糊→精准</text>
      <text x="20" y="220" class="feature-item">异常检测 | 告警聚合 | 链路追踪</text>
    </g>
    
    <!-- App Module 2: Automated Operations -->
    <g transform="translate(390, 80)">
      <rect x="0" y="0" width="340" height="260" rx="10" fill="url(#app-gradient)" stroke="#3b82f6" stroke-width="1.5" />
      <text x="170" y="35" class="box-title">⚙️ 智能运维中台</text>
      <text x="20" y="65" class="feature-highlight">🎯 支撑价值1&amp;2：自动化执行</text>
      <text x="20" y="90" class="feature-item">🎛️ 统一控制台：一站式运维</text>
      <text x="20" y="110" class="feature-item">🔧 原子操作库：标准化能力</text>
      <text x="20" y="130" class="feature-item">♻️ 生命周期：全程自动管理</text>
      <text x="20" y="150" class="feature-item">🚚 批量操作：热迁移/轮转</text>
      <text x="20" y="175" class="feature-highlight">💡 关键价值：</text>
      <text x="20" y="195" class="feature-item">效率提升 | 风险降低 | 成本优化</text>
      <text x="20" y="220" class="feature-item">智能调度 | 任务编排 | 自助服务</text>
    </g>
    
    <!-- App Module 3: Change Management - PRIMARY -->
    <g transform="translate(750, 80)">
      <rect x="0" y="0" width="340" height="260" rx="10" fill="url(#app-gradient)" stroke="#1e40af" stroke-width="3" />
      <circle cx="20" cy="20" r="12" fill="#f59e0b"/>
      <text x="20" y="25" style="font-size: 10px; fill: white; text-anchor: middle; font-weight: bold;">2</text>
      <text x="170" y="35" class="box-title">🛡️ 智能变更防护中心</text>
      <text x="20" y="65" class="feature-highlight">🎯 价值2核心：零风险变更 + 自愈防护</text>
      <text x="20" y="90" class="feature-item">📋 风险评估：变更影响预判</text>
      <text x="20" y="110" class="feature-item">🎯 灰度发布：渐进式安全部署</text>
      <text x="20" y="130" class="feature-item">⚡ 毫秒熔断：实时风险监测</text>
      <text x="20" y="150" class="feature-item">🔄 自动回滚：故障快速恢复</text>
      <text x="20" y="175" class="feature-highlight">💡 关键突破：</text>
      <text x="20" y="195" class="feature-item">高风险→零风险 | 被动→主动防护</text>
      <text x="20" y="220" class="feature-item">降级预案 | 质量门禁 | 策略编排</text>
    </g>
    
    <!-- App Module 4: Incident Management - PRIMARY -->
    <g transform="translate(1110, 80)">
      <rect x="0" y="0" width="340" height="260" rx="10" fill="url(#app-gradient)" stroke="#1e40af" stroke-width="3" />
      <circle cx="20" cy="20" r="12" fill="#f59e0b"/>
      <text x="20" y="25" style="font-size: 10px; fill: white; text-anchor: middle; font-weight: bold;">3</text>
      <text x="170" y="35" class="box-title">📊 智能进化闭环中心</text>
      <text x="20" y="65" class="feature-highlight">🎯 价值3核心：数据驱动 + 持续进化</text>
      <text x="20" y="90" class="feature-item">🎯 故障中心：统一事件管理</text>
      <text x="20" y="110" class="feature-item">📈 全生命周期：端到端跟踪</text>
      <text x="20" y="130" class="feature-item">🔍 智能分析：工单数据挖掘</text>
      <text x="20" y="150" class="feature-item">🧠 复盘改进：知识沉淀循环</text>
      <text x="20" y="175" class="feature-highlight">💡 关键突破：</text>
      <text x="20" y="195" class="feature-item">故障→经验 | 被动→主动进化</text>
      <text x="20" y="220" class="feature-item">SLA监控 | 知识更新 | 持续优化</text>
    </g>
    
    <!-- Enhanced Flow Arrows with Labels -->
    <path d="M375,200 L385,200" class="connection-line" />
    <text x="380" y="190" style="font-size: 10px; fill: #4f46e5; text-anchor: middle;">数据流</text>
    <path d="M735,200 L745,200" class="connection-line" />
    <text x="740" y="190" style="font-size: 10px; fill: #4f46e5; text-anchor: middle;">执行流</text>
    <path d="M1095,200 L1105,200" class="connection-line" />
    <text x="1100" y="190" style="font-size: 10px; fill: #4f46e5; text-anchor: middle;">反馈流</text>
  </g>

  <!-- Layer Separator -->
  <line x1="50" y1="590" x2="1550" y2="590" class="layer-separator" />

  <!-- Layer 2: Engine Layer -->
  <g transform="translate(50, 650)">
    <text x="750" y="30" class="layer-title">🧠 核心能力引擎层 - 智能驱动内核</text>
    <rect x="0" y="50" width="1500" height="300" rx="15" fill="white" stroke="#e2e8f0" stroke-width="1" filter="url(#drop-shadow)" />

    <!-- Engine Support Indicators -->
    <g opacity="0.4">
      <path d="M200,0 L200,50" stroke="#10b981" stroke-width="3" stroke-dasharray="3,3"/>
      <path d="M575,0 L575,50" stroke="#10b981" stroke-width="3" stroke-dasharray="3,3"/>
      <path d="M925,0 L925,50" stroke="#10b981" stroke-width="3" stroke-dasharray="3,3"/>
      <path d="M1305,0 L1305,50" stroke="#10b981" stroke-width="3" stroke-dasharray="3,3"/>
    </g>
    
    <!-- Engine 1: AIOps -->
    <g transform="translate(30, 80)">
      <rect x="0" y="0" width="350" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="175" y="30" class="box-title">🤖 AIOps智能引擎</text>
      <text x="20" y="60" class="feature-highlight">🎯 核心价值：智能感知与预测</text>
      <text x="20" y="85" class="feature-item">🔍 异常检测：指标/日志智能分析</text>
      <text x="20" y="105" class="feature-item">🧠 根因分析：故障链路自动推理</text>
      <text x="20" y="125" class="feature-item">📊 影响评估：业务影响面量化</text>
      <text x="20" y="145" class="feature-item">🔮 容量预测：资源需求智能规划</text>
      <text x="20" y="170" class="feature-highlight">💡 关键技术：</text>
      <text x="20" y="190" class="feature-item">全链路追踪 | 服务拓扑 | 时序预测</text>
      <text x="20" y="210" class="feature-item">关联分析 | 标签画像 | 异常聚类</text>
    </g>
    
    <!-- Engine 2: Knowledge -->
    <g transform="translate(400, 80)">
      <rect x="0" y="0" width="350" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="175" y="30" class="box-title">📚 知识策略引擎</text>
      <text x="20" y="60" class="feature-highlight">🎯 核心价值：经验沉淀与复用</text>
      <text x="20" y="85" class="feature-item">🩺 诊断知识：故障模式专家库</text>
      <text x="20" y="105" class="feature-item">🛡️ 自愈预案：标准化处置流程</text>
      <text x="20" y="125" class="feature-item">🚀 发布策略：风险控制最佳实践</text>
      <text x="20" y="145" class="feature-item">🎭 演练场景：故障注入测试库</text>
      <text x="20" y="170" class="feature-highlight">💡 关键技术：</text>
      <text x="20" y="190" class="feature-item">知识图谱 | 规则引擎 | 策略模板</text>
      <text x="20" y="210" class="feature-item">专家系统 | 案例推理 | 智能匹配</text>
    </g>
    
    <!-- Engine 3: Orchestration -->
    <g transform="translate(770, 80)">
      <rect x="0" y="0" width="350" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="175" y="30" class="box-title">🔧 执行编排引擎</text>
      <text x="20" y="60" class="feature-highlight">🎯 核心价值：可靠自动化执行</text>
      <text x="20" y="85" class="feature-item">⚡ 原子操作：标准化运维指令库</text>
      <text x="20" y="105" class="feature-item">🔄 工作流：复杂场景智能编排</text>
      <text x="20" y="125" class="feature-item">🚚 迁移执行：热/冷迁移自动化</text>
      <text x="20" y="145" class="feature-item">🛡️ 安全检查：前后置验证框架</text>
      <text x="20" y="170" class="feature-highlight">💡 关键技术：</text>
      <text x="20" y="190" class="feature-item">任务调度 | 并发控制 | 状态监控</text>
      <text x="20" y="210" class="feature-item">回滚机制 | 限流保护 | 执行审计</text>
    </g>
    
    <!-- Engine 4: Governance -->
    <g transform="translate(1140, 80)">
      <rect x="0" y="0" width="330" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="165" y="30" class="box-title">🏛️ 平台治理引擎</text>
      <text x="20" y="60" class="feature-highlight">🎯 核心价值：统一管控与合规</text>
      <text x="20" y="85" class="feature-item">📋 配置管理：CMDB统一数据源</text>
      <text x="20" y="105" class="feature-item">📢 通知中心：多渠道智能推送</text>
      <text x="20" y="125" class="feature-item">📊 SLA管理：服务质量量化监控</text>
      <text x="20" y="145" class="feature-item">🔐 权限控制：细粒度安全管控</text>
      <text x="20" y="170" class="feature-highlight">💡 关键技术：</text>
      <text x="20" y="190" class="feature-item">工单系统 | 报表引擎 | 审计日志</text>
      <text x="20" y="210" class="feature-item">成本分析 | 合规检查 | 流量控制</text>
    </g>
  </g>

  <!-- Layer Separator -->
  <line x1="50" y1="930" x2="1550" y2="930" class="layer-separator" />

  <!-- Layer 3: Foundation Layer -->
  <g transform="translate(50, 990)">
    <text x="750" y="30" class="layer-title">🏗️ 数据与平台基础层 - 能力底座</text>
    <rect x="0" y="50" width="1500" height="140" rx="15" fill="white" stroke="#e2e8f0" stroke-width="1" filter="url(#drop-shadow)" />

    <!-- Foundation Support Indicators -->
    <g opacity="0.4">
      <path d="M375,0 L375,50" stroke="#f59e0b" stroke-width="4" stroke-dasharray="2,2"/>
      <path d="M1125,0 L1125,50" stroke="#f59e0b" stroke-width="4" stroke-dasharray="2,2"/>
    </g>
    
    <!-- Foundation 1: Data Infrastructure -->
    <g transform="translate(30, 80)">
      <rect x="0" y="0" width="720" height="100" rx="10" fill="url(#platform-gradient)" stroke="#f59e0b" stroke-width="1.5" />
      <text x="360" y="30" class="box-title">🏗️ 数据基础设施平台</text>
      <text x="20" y="55" class="feature-highlight">基础能力：</text>
      <text x="20" y="75" class="feature-item">多源数据采集 | 海量存储(Metrics/Logs/Traces) | 实时流计算 | 数据治理 | 统一数据服务API</text>
    </g>

    <!-- Foundation 2: Intelligence Engine -->
    <g transform="translate(770, 80)">
      <rect x="0" y="0" width="700" height="100" rx="10" fill="url(#platform-gradient)" stroke="#f59e0b" stroke-width="1.5" />
      <text x="350" y="30" class="box-title">🧠 智能分析引擎</text>
      <text x="20" y="55" class="feature-highlight">智能算法：</text>
      <text x="20" y="75" class="feature-item">机器学习模型训练 | 深度关联挖掘 | 智能标签体系 | 预测算法库 | 知识图谱推理</text>
    </g>
  </g>

  <!-- Enhanced Vertical Flow Indicators -->
  <g opacity="0.7">
    <!-- App Layer to Engine Layer -->
    <path d="M800,620 L800,650" class="connection-line" stroke-width="3"/>
    <text x="810" y="635" style="font-size: 12px; fill: #4f46e5; font-weight: bold;">能力支撑</text>

    <!-- Engine Layer to Foundation Layer -->
    <path d="M800,960 L800,990" class="connection-line" stroke-width="3"/>
    <text x="810" y="975" style="font-size: 12px; fill: #4f46e5; font-weight: bold;">数据驱动</text>

    <!-- Value to App connections -->
    <path d="M290,280 L290,290" class="connection-line" stroke="#f59e0b" stroke-width="2"/>
    <path d="M800,280 L800,290" class="connection-line" stroke="#f59e0b" stroke-width="2"/>
    <path d="M1310,280 L1310,290" class="connection-line" stroke="#f59e0b" stroke-width="2"/>
  </g>

  <!-- Value Realization Path -->
  <g transform="translate(50, 1180)">
    <rect x="0" y="0" width="1500" height="80" rx="10" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" opacity="0.8"/>
    <text x="750" y="25" style="font-size: 16px; fill: #1e40af; text-anchor: middle; font-weight: bold;">💡 价值实现路径</text>
    <text x="750" y="50" style="font-size: 14px; fill: #475569; text-anchor: middle;">
      数据基础设施 → 智能引擎驱动 → 产品应用承载 → 三大价值实现 → 运维体系进化
    </text>
  </g>

  <!-- Footer -->
  <text x="800" y="1300" style="font-size: 14px; fill: #64748b; text-anchor: middle; font-style: italic;">
    CloudBot 智能运维系统 - 构建下一代智能化运维生态
  </text>

</svg>
