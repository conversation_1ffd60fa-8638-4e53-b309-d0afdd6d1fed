<svg width="1600" height="1400" viewBox="0 0 1600 1400" xmlns="http://www.w3.org/2000/svg" font-family="'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif">
  
  <defs>
    <!-- Enhanced Gradients -->
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fafbfc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="header-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="value-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fef3c7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="app-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#eff6ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dbeafe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="engine-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f0fdf4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dcfce7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="platform-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fefce8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:1" />
    </linearGradient>
    
    <!-- Enhanced Arrow Marker -->
    <marker id="arrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,8 L12,4 z" fill="#4f46e5" opacity="0.8"/>
    </marker>
    
    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- Styles -->
    <style>
      .main-title { 
        font-size: 32px; 
        font-weight: 700; 
        fill: white; 
        text-anchor: middle; 
        letter-spacing: 0.5px;
      }
      
      .layer-title { 
        font-size: 24px; 
        font-weight: 600; 
        fill: #1e3a8a; 
        text-anchor: middle; 
        letter-spacing: 0.3px;
      }
      
      .value-title { 
        font-size: 18px; 
        font-weight: 700; 
        fill: #92400e; 
        text-anchor: middle; 
      }
      
      .value-desc { 
        font-size: 13px; 
        fill: #78350f; 
        font-weight: 400;
        line-height: 1.4;
      }
      
      .box-title { 
        font-size: 16px; 
        font-weight: 600; 
        fill: #1e40af; 
        text-anchor: middle; 
      }
      
      .feature-item { 
        font-size: 12px; 
        fill: #475569; 
        font-weight: 400;
      }
      
      .feature-highlight { 
        font-size: 12px; 
        fill: #1e40af; 
        font-weight: 500;
      }
      
      .connection-line { 
        stroke: #4f46e5; 
        stroke-width: 2; 
        fill: none; 
        marker-end: url(#arrow);
        opacity: 0.7;
      }
      
      .layer-separator {
        stroke: #e2e8f0;
        stroke-width: 1;
        opacity: 0.5;
      }
    </style>
  </defs>

  <!-- Background -->
  <rect width="1600" height="1400" fill="url(#bg-gradient)" />

  <!-- Header Section -->
  <rect x="0" y="0" width="1600" height="80" fill="url(#header-gradient)" filter="url(#drop-shadow)" />
  <text x="800" y="50" class="main-title">CloudBot 智能运维系统 - 核心价值架构</text>
  
  <!-- Core Values Section -->
  <g transform="translate(50, 100)">
    <text x="750" y="30" class="layer-title">🎯 三大核心价值主张</text>
    
    <!-- Value Proposition 1 -->
    <g transform="translate(0, 60)">
      <rect x="0" y="0" width="480" height="110" rx="12" fill="url(#value-gradient)" filter="url(#drop-shadow)" />
      <text x="240" y="30" class="value-title">🔍 智能感知与精准诊断</text>
      <text x="20" y="55" class="value-desc">
        <tspan x="20" dy="0">• 全链路数据采集与AIOps算法驱动</tspan>
        <tspan x="20" dy="16">• 从被动响应转向主动预警</tspan>
        <tspan x="20" dy="16">• 快速定界定位，洞察系统健康状态</tspan>
      </text>
    </g>
    
    <!-- Value Proposition 2 -->
    <g transform="translate(510, 60)">
      <rect x="0" y="0" width="480" height="110" rx="12" fill="url(#value-gradient)" filter="url(#drop-shadow)" />
      <text x="240" y="30" class="value-title">🛡️ 稳定变更与智能熔断</text>
      <text x="20" y="55" class="value-desc">
        <tspan x="20" dy="0">• 知识策略与编排引擎协同</tspan>
        <tspan x="20" dy="16">• 可靠的灰度发布与变更风控</tspan>
        <tspan x="20" dy="16">• 故障自愈能力，保障业务连续性</tspan>
      </text>
    </g>
    
    <!-- Value Proposition 3 -->
    <g transform="translate(1020, 60)">
      <rect x="0" y="0" width="480" height="110" rx="12" fill="url(#value-gradient)" filter="url(#drop-shadow)" />
      <text x="240" y="30" class="value-title">📊 数据驱动与闭环运营</text>
      <text x="20" y="55" class="value-desc">
        <tspan x="20" dy="0">• 运维数据与案例沉淀</tspan>
        <tspan x="20" dy="16">• 工单、报表实现故障完整跟踪</tspan>
        <tspan x="20" dy="16">• 复盘改进，驱动运维体系进化</tspan>
      </text>
    </g>
  </g>

  <!-- Layer 1: Application Layer -->
  <g transform="translate(50, 290)">
    <text x="750" y="30" class="layer-title">📱 产品应用层</text>
    <rect x="0" y="50" width="1500" height="280" rx="15" fill="white" stroke="#e2e8f0" stroke-width="1" filter="url(#drop-shadow)" />
    
    <!-- App Module 1: Collection &amp; Diagnosis -->
    <g transform="translate(30, 80)">
      <rect x="0" y="0" width="340" height="220" rx="10" fill="url(#app-gradient)" stroke="#3b82f6" stroke-width="1.5" />
      <text x="170" y="30" class="box-title">🔍 采集与诊断</text>
      <text x="20" y="60" class="feature-highlight">核心能力：</text>
      <text x="20" y="80" class="feature-item">• 全链路定界 &amp; 实时追踪</text>
      <text x="20" y="100" class="feature-item">• 影响面智能分析算法</text>
      <text x="20" y="120" class="feature-item">• 实例健康状态诊断</text>
      <text x="20" y="140" class="feature-item">• AI诊断助手与建议</text>
      <text x="20" y="160" class="feature-item">• 异常检测与告警聚合</text>
      <text x="20" y="180" class="feature-item">• 根因分析与推荐</text>
    </g>
    
    <!-- App Module 2: Automated Operations -->
    <g transform="translate(390, 80)">
      <rect x="0" y="0" width="340" height="220" rx="10" fill="url(#app-gradient)" stroke="#3b82f6" stroke-width="1.5" />
      <text x="170" y="30" class="box-title">⚙️ 自动化运维</text>
      <text x="20" y="60" class="feature-highlight">核心能力：</text>
      <text x="20" y="80" class="feature-item">• 统一运维控制台</text>
      <text x="20" y="100" class="feature-item">• 自助运维原子操作库</text>
      <text x="20" y="120" class="feature-item">• 资源生命周期管理</text>
      <text x="20" y="140" class="feature-item">• 批量热迁移/轮转</text>
      <text x="20" y="160" class="feature-item">• 智能调度与优化</text>
      <text x="20" y="180" class="feature-item">• 运维任务编排</text>
    </g>
    
    <!-- App Module 3: Change Management -->
    <g transform="translate(750, 80)">
      <rect x="0" y="0" width="340" height="220" rx="10" fill="url(#app-gradient)" stroke="#3b82f6" stroke-width="1.5" />
      <text x="170" y="30" class="box-title">🚀 变更发布与熔断</text>
      <text x="20" y="60" class="feature-highlight">核心能力：</text>
      <text x="20" y="80" class="feature-item">• 变更规划与风险评估</text>
      <text x="20" y="100" class="feature-item">• 灰度发布策略编排</text>
      <text x="20" y="120" class="feature-item">• 智能风险监测</text>
      <text x="20" y="140" class="feature-item">• 自动熔断与回滚</text>
      <text x="20" y="160" class="feature-item">• 故障降级预案</text>
      <text x="20" y="180" class="feature-item">• 发布质量门禁</text>
    </g>
    
    <!-- App Module 4: Incident Management -->
    <g transform="translate(1110, 80)">
      <rect x="0" y="0" width="340" height="220" rx="10" fill="url(#app-gradient)" stroke="#3b82f6" stroke-width="1.5" />
      <text x="170" y="30" class="box-title">📋 故障跟踪与闭环</text>
      <text x="20" y="60" class="feature-highlight">核心能力：</text>
      <text x="20" y="80" class="feature-item">• 统一故障管理中心</text>
      <text x="20" y="100" class="feature-item">• 客户事件全生命周期</text>
      <text x="20" y="120" class="feature-item">• 故障工单智能分析</text>
      <text x="20" y="140" class="feature-item">• 根因复盘与改进</text>
      <text x="20" y="160" class="feature-item">• SLA监控与报告</text>
      <text x="20" y="180" class="feature-item">• 知识库持续更新</text>
    </g>
    
    <!-- Flow Arrows -->
    <path d="M375,190 L385,190" class="connection-line" />
    <path d="M735,190 L745,190" class="connection-line" />
    <path d="M1095,190 L1105,190" class="connection-line" />
  </g>

  <!-- Layer Separator -->
  <line x1="50" y1="590" x2="1550" y2="590" class="layer-separator" />

  <!-- Layer 2: Engine Layer -->
  <g transform="translate(50, 610)">
    <text x="750" y="30" class="layer-title">🧠 核心能力引擎层</text>
    <rect x="0" y="50" width="1500" height="300" rx="15" fill="white" stroke="#e2e8f0" stroke-width="1" filter="url(#drop-shadow)" />
    
    <!-- Engine 1: AIOps -->
    <g transform="translate(30, 80)">
      <rect x="0" y="0" width="350" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="175" y="30" class="box-title">🤖 AIOps数据引擎</text>
      <text x="20" y="60" class="feature-highlight">智能算法：</text>
      <text x="20" y="80" class="feature-item">• 全链路追踪 &amp; 服务拓扑</text>
      <text x="20" y="100" class="feature-item">• 指标/日志异常检测</text>
      <text x="20" y="120" class="feature-item">• 宕机预测与根因分析</text>
      <text x="20" y="140" class="feature-item">• 故障影响面智能评估</text>
      <text x="20" y="160" class="feature-item">• 服务标签画像生成</text>
      <text x="20" y="180" class="feature-item">• 时序预测与容量规划</text>
      <text x="20" y="200" class="feature-item">• 关联分析与推荐</text>
    </g>
    
    <!-- Engine 2: Knowledge -->
    <g transform="translate(400, 80)">
      <rect x="0" y="0" width="350" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="175" y="30" class="box-title">📚 知识策略引擎</text>
      <text x="20" y="60" class="feature-highlight">知识沉淀：</text>
      <text x="20" y="80" class="feature-item">• 诊断助手知识库</text>
      <text x="20" y="100" class="feature-item">• 故障自愈预案库</text>
      <text x="20" y="120" class="feature-item">• 变更/灰度发布策略</text>
      <text x="20" y="140" class="feature-item">• 故障演练场景库</text>
      <text x="20" y="160" class="feature-item">• 物理机运维规则</text>
      <text x="20" y="180" class="feature-item">• 最佳实践模板</text>
      <text x="20" y="200" class="feature-item">• 运维知识图谱</text>
    </g>
    
    <!-- Engine 3: Orchestration -->
    <g transform="translate(770, 80)">
      <rect x="0" y="0" width="350" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="175" y="30" class="box-title">🔧 执行编排引擎</text>
      <text x="20" y="60" class="feature-highlight">执行能力：</text>
      <text x="20" y="80" class="feature-item">• 运维原子能力库(P-CMD)</text>
      <text x="20" y="100" class="feature-item">• 通用工作流编排</text>
      <text x="20" y="120" class="feature-item">• 任务调度与执行</text>
      <text x="20" y="140" class="feature-item">• 热/冷迁移执行器</text>
      <text x="20" y="160" class="feature-item">• 前后置检查框架</text>
      <text x="20" y="180" class="feature-item">• 并发控制与限流</text>
      <text x="20" y="200" class="feature-item">• 执行状态监控</text>
    </g>
    
    <!-- Engine 4: Governance -->
    <g transform="translate(1140, 80)">
      <rect x="0" y="0" width="330" height="240" rx="10" fill="url(#engine-gradient)" stroke="#10b981" stroke-width="1.5" />
      <text x="165" y="30" class="box-title">🏛️ 平台治理引擎</text>
      <text x="20" y="60" class="feature-highlight">治理能力：</text>
      <text x="20" y="80" class="feature-item">• 统一配置管理(CMDB)</text>
      <text x="20" y="100" class="feature-item">• 运维场景调度配置</text>
      <text x="20" y="120" class="feature-item">• 通知管理(模板/规则)</text>
      <text x="20" y="140" class="feature-item">• 工单/报表/SLA管理</text>
      <text x="20" y="160" class="feature-item">• 权限/白名单/流控</text>
      <text x="20" y="180" class="feature-item">• 审计日志与合规</text>
      <text x="20" y="200" class="feature-item">• 成本分析与优化</text>
    </g>
  </g>

  <!-- Layer Separator -->
  <line x1="50" y1="930" x2="1550" y2="930" class="layer-separator" />

  <!-- Layer 3: Foundation Layer -->
  <g transform="translate(50, 950)">
    <text x="750" y="30" class="layer-title">🏗️ 数据与平台基础层</text>
    <rect x="0" y="50" width="1500" height="140" rx="15" fill="white" stroke="#e2e8f0" stroke-width="1" filter="url(#drop-shadow)" />
    
    <!-- Foundation 1: Data Platform -->
    <g transform="translate(30, 80)">
      <rect x="0" y="0" width="720" height="100" rx="10" fill="url(#platform-gradient)" stroke="#f59e0b" stroke-width="1.5" />
      <text x="360" y="30" class="box-title">📊 智能数据与分析平台</text>
      <text x="20" y="55" class="feature-highlight">数据能力：</text>
      <text x="20" y="75" class="feature-item">数据采集 | 统一存储(Metrics/Logs/Traces) | 钻取分析 | 数据标注 | 统一数据服务 | 实时计算</text>
    </g>
    
    <!-- Foundation 2: Analysis Capabilities -->
    <g transform="translate(770, 80)">
      <rect x="0" y="0" width="700" height="100" rx="10" fill="url(#platform-gradient)" stroke="#f59e0b" stroke-width="1.5" />
      <text x="350" y="30" class="box-title">🔬 核心数据分析能力</text>
      <text x="20" y="55" class="feature-highlight">分析能力：</text>
      <text x="20" y="75" class="feature-item">关联分析与根因推荐 | 时序预测 | 日志聚类挖掘 | 运维知识图谱构建 | 智能标签生成</text>
    </g>
  </g>

  <!-- Vertical Flow Indicators -->
  <g opacity="0.6">
    <path d="M800,280 L800,300" class="connection-line" />
    <path d="M800,590 L800,610" class="connection-line" />
    <path d="M800,920 L800,940" class="connection-line" />
  </g>

  <!-- Footer -->
  <text x="800" y="1350" style="font-size: 14px; fill: #64748b; text-anchor: middle; font-style: italic;">
    CloudBot 智能运维系统 - 构建下一代智能化运维生态
  </text>

</svg>
